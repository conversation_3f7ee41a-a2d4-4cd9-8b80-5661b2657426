# 專案構建說明

## 問題解決

原本的錯誤已修正：
- ✅ 移除 CDN 版本的 Tailwind CSS 和 Babel
- ✅ 修正 `require is not defined` 錯誤
- ✅ 設置生產環境構建流程
- ✅ 使用本地預編譯的 JavaScript 和 CSS

## 構建指令

### 生產環境構建
```bash
npm run build
```
或使用批次檔：
```bash
build.bat
```

### 開發模式（監視檔案變化）
```bash
npm run dev
```
或使用批次檔：
```bash
dev.bat
```

### 個別構建指令
- `npm run build-css-prod` - 構建並壓縮 CSS
- `npm run build-css` - 構建 CSS（監視模式）
- `npm run build-js` - 構建 JavaScript
- `npm run build-js-watch` - 構建 JavaScript（監視模式）

## 檔案結構

```
lopokao/
├── src/
│   └── input.css          # Tailwind CSS 入口檔案
├── dist/                  # 構建輸出目錄
│   ├── styles.css         # 編譯後的 CSS
│   ├── app.js            # 編譯後的主應用程式
│   ├── components/       # 編譯後的組件
│   └── utils/            # 編譯後的工具函式
├── components/           # 原始組件檔案
├── utils/               # 原始工具函式
├── styles/              # 自定義 CSS 檔案
└── index.html           # 主頁面（已更新為使用構建檔案）
```

## 部署注意事項

1. **生產環境部署前**，請執行 `npm run build` 確保所有檔案都是最新的
2. **dist/ 目錄**包含所有構建後的檔案，這些檔案已經過 Babel 轉換和 Tailwind CSS 處理
3. **index.html** 現在引用 dist/ 目錄中的檔案，不再使用 CDN 版本
4. **不再需要**瀏覽器端的 Babel 轉換，提升載入效能

## 開發工作流程

1. 修改原始檔案（components/, utils/, app.js）
2. 執行 `dev.bat` 啟動監視模式
3. 檔案會自動重新構建到 dist/ 目錄
4. 重新整理瀏覽器查看變更

## 錯誤修正摘要

- **CDN 警告**：移除 CDN 依賴，使用本地構建
- **Babel 警告**：移除瀏覽器端轉換，使用預編譯
- **require 錯誤**：修正 import 語法問題
- **生產環境優化**：CSS 和 JS 都經過適當的處理和壓縮
