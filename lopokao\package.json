{"scripts": {"build-css": "tailwindcss -i ./src/input.css -o ./dist/styles.css --watch", "build-css-prod": "tailwindcss -i ./src/input.css -o ./dist/styles.css --minify", "build-js": "babel app.js components utils -d dist --presets=@babel/preset-env,@babel/preset-react", "build-js-watch": "babel app.js components utils -d dist --presets=@babel/preset-env,@babel/preset-react --watch", "build": "npm run build-css-prod && npm run build-js", "dev": "npm run build-css & npm run build-js-watch"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@tailwindcss/cli": "^4.1.8", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}}